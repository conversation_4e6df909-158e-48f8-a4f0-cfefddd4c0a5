import React, { useState, useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import mammoth from 'mammoth';

interface QuillEditorProps {
  onChange: (content: string) => void;
}

const QuillEditor: React.FC<QuillEditorProps> = ({ onChange }) => {
  const [editorContent, setEditorContent] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link', 'image'],
      ['clean'],
      // Add a custom button for document upload
      ['document']
    ]
  };

  const handleDocumentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if the file is a .docx
    if (file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      alert('Please upload a .docx file');
      return;
    }

    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      
      // Set the converted HTML content to Quill
      setEditorContent(result.value);
      onChange(result.value);
    } catch (error) {
      console.error('Error converting document:', error);
      alert('Error converting document');
    }
  };

  const handleEditorChange = (content: string) => {
    setEditorContent(content);
    onChange(content);
  };

  return (
    <div className="editor-container">
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleDocumentUpload}
        accept=".docx"
        style={{ display: 'none' }}
      />
      
      {/* Custom toolbar button */}
      <button 
        onClick={() => fileInputRef.current?.click()}
        className="upload-doc-btn"
      >
        Upload DOCX
      </button>

      <ReactQuill
        theme="snow"
        value={editorContent}
        onChange={handleEditorChange}
        modules={modules}
      />
    </div>
  );
};

export default QuillEditor;