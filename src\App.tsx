import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, Download, Save, FileText, Plus, Trash2, Edit3, Eye, Layout, Type, Image as ImageIcon } from 'lucide-react';
import { Designer, Viewer } from '@pdfme/ui';
import { generate } from '@pdfme/generator';
import { BLANK_PDF, getInputFromTemplate } from '@pdfme/common';

const App = () => {
  const [currentMode, setCurrentMode] = useState('designer');
  const [template, setTemplate] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [documentName, setDocumentName] = useState('New PDF Template');
  const [templates, setTemplates] = useState([]);
  const [inputs, setInputs] = useState([{}]);
  const [pdfUrl, setPdfUrl] = useState('');
  const [designerInstance, setDesignerInstance] = useState(null);
  const [viewerInstance, setViewerInstance] = useState(null);
  
  const designerRef = useRef(null);
  const viewerRef = useRef(null);
  const fileInputRef = useRef(null);

  // Initialize with default template
  useEffect(() => {
    const defaultTemplate = {
      basePdf: BLANK_PDF,
      schemas: [
        {
          title: {
            type: 'text',
            position: { x: 50, y: 50 },
            width: 200,
            height: 30,
            alignment: 'left',
            fontSize: 20,
            fontColor: '#000000',
            fontName: 'NotoSerifJP-Regular'
          },
          subtitle: {
            type: 'text',
            position: { x: 50, y: 100 },
            width: 300,
            height: 20,
            alignment: 'left',
            fontSize: 14,
            fontColor: '#666666',
            fontName: 'NotoSerifJP-Regular'
          },
          content: {
            type: 'text',
            position: { x: 50, y: 150 },
            width: 500,
            height: 200,
            alignment: 'left',
            fontSize: 12,
            fontColor: '#333333',
            fontName: 'NotoSerifJP-Regular'
          }
        }
      ]
    };
    
    setTemplate(defaultTemplate);
    
    // Generate initial inputs from template
    const initialInputs = getInputFromTemplate(defaultTemplate);
    initialInputs[0] = {
      title: 'Sample Document Title',
      subtitle: 'This is a subtitle',
      content: 'This is the main content area. You can edit this text and see it update in real-time.'
    };
    setInputs(initialInputs);
  }, []);

  // Initialize Designer
  const initializeDesigner = useCallback(async () => {
    if (!template || !designerRef.current) return;

    try {
      // Clear previous instance
      if (designerInstance) {
        designerInstance.destroy();
      }
      
      // Clear container
      designerRef.current.innerHTML = '';

      const designer = new Designer({
        domContainer: designerRef.current,
        template: template,
        options: {
          theme: {
            token: {
              colorPrimary: '#3b82f6',
            },
          },
          labels: {
            'fieldsList.title': 'Fields',
            'fieldsList.add': 'Add Field',
            'edit.delete': 'Delete',
            'edit.duplicate': 'Duplicate',
            'edit.copy': 'Copy',
            'edit.paste': 'Paste',
          },
        },
      });

      // Listen for template changes
      designer.onChangeTemplate((newTemplate) => {
        setTemplate(newTemplate);
        // Update inputs when template changes
        const newInputs = getInputFromTemplate(newTemplate);
        if (newInputs.length > 0 && inputs.length > 0) {
          // Preserve existing values where possible
          const mergedInputs = newInputs.map((inputSchema, index) => {
            const existingInput = inputs[index] || {};
            const mergedInput = {};
            Object.keys(inputSchema).forEach(key => {
              mergedInput[key] = existingInput[key] || '';
            });
            return mergedInput;
          });
          setInputs(mergedInputs);
        } else {
          setInputs(newInputs);
        }
      });

      setDesignerInstance(designer);
    } catch (error) {
      console.error('Error initializing designer:', error);
    }
  }, [template, designerInstance, inputs]);

  // Initialize Viewer
  const initializeViewer = useCallback(async () => {
    if (!template || !viewerRef.current) return;

    try {
      // Clear previous instance
      if (viewerInstance) {
        viewerInstance.destroy();
      }
      
      // Clear container
      viewerRef.current.innerHTML = '';

      const viewer = new Viewer({
        domContainer: viewerRef.current,
        template: template,
        inputs: inputs,
        options: {
          theme: {
            token: {
              colorPrimary: '#10b981',
            },
          },
        },
      });

      setViewerInstance(viewer);
    } catch (error) {
      console.error('Error initializing viewer:', error);
    }
  }, [template, inputs, viewerInstance]);

  // Generate PDF
  const generatePDF = useCallback(async () => {
    if (!template) {
      alert('No template available');
      return;
    }

    setIsLoading(true);
    try {
      const pdf = await generate({
        template: template,
        inputs: inputs,
      });

      // Create blob URL for preview
      const blob = new Blob([pdf.buffer], { type: 'application/pdf' });
      
      // Clean up previous URL
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
      
      const url = URL.createObjectURL(blob);
      setPdfUrl(url);

      return pdf;
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, [template, inputs, pdfUrl]);

  // Load existing PDF template
  const loadPDFTemplate = useCallback(async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      alert('Please select a PDF file');
      return;
    }

    setIsLoading(true);
    try {
      const arrayBuffer = await file.arrayBuffer();
      const base64 = btoa(
        new Uint8Array(arrayBuffer)
          .reduce((data, byte) => data + String.fromCharCode(byte), '')
      );

      const newTemplate = {
        basePdf: base64,
        schemas: [
          {
            field1: {
              type: 'text',
              position: { x: 50, y: 50 },
              width: 200,
              height: 30,
              alignment: 'left',
              fontSize: 16,
              fontColor: '#000000',
              fontName: 'NotoSerifJP-Regular'
            },
            field2: {
              type: 'text',
              position: { x: 50, y: 100 },
              width: 300,
              height: 50,
              alignment: 'left',
              fontSize: 12,
              fontColor: '#333333',
              fontName: 'NotoSerifJP-Regular'
            }
          }
        ]
      };

      setTemplate(newTemplate);
      const newInputs = getInputFromTemplate(newTemplate);
      newInputs[0] = {
        field1: 'Sample text',
        field2: 'Another sample text'
      };
      setInputs(newInputs);
      setDocumentName(file.name.replace('.pdf', ''));
    } catch (error) {
      console.error('Error loading PDF template:', error);
      alert('Error loading PDF file: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create blank template
  const createBlankTemplate = useCallback(() => {
    const blankTemplate = {
      basePdf: BLANK_PDF,
      schemas: [
        {
          title: {
            type: 'text',
            position: { x: 50, y: 50 },
            width: 200,
            height: 30,
            alignment: 'left',
            fontSize: 20,
            fontColor: '#000000',
            fontName: 'NotoSerifJP-Regular'
          },
          subtitle: {
            type: 'text',
            position: { x: 50, y: 100 },
            width: 300,
            height: 20,
            alignment: 'left',
            fontSize: 14,
            fontColor: '#666666',
            fontName: 'NotoSerifJP-Regular'
          },
          content: {
            type: 'text',
            position: { x: 50, y: 150 },
            width: 500,
            height: 200,
            alignment: 'left',
            fontSize: 12,
            fontColor: '#333333',
            fontName: 'NotoSerifJP-Regular'
          }
        }
      ]
    };

    setTemplate(blankTemplate);
    const newInputs = getInputFromTemplate(blankTemplate);
    newInputs[0] = { 
      title: 'New Document Title', 
      subtitle: 'Document Subtitle',
      content: 'Enter your content here. You can edit this in the designer mode or directly in the form fields.' 
    };
    setInputs(newInputs);
    setDocumentName('New PDF Template');
  }, []);

  // Save template
  const saveTemplate = useCallback(() => {
    if (!template) {
      alert('No template to save');
      return;
    }

    const templateName = prompt('Enter template name:', documentName);
    if (!templateName) return;

    const newTemplate = {
      id: Date.now(),
      name: templateName,
      template: template,
      inputs: inputs,
      createdAt: new Date().toISOString()
    };

    setTemplates(prev => [...prev, newTemplate]);
    alert('Template saved successfully!');
  }, [template, inputs, documentName]);

  // Load saved template
  const loadSavedTemplate = useCallback((savedTemplate) => {
    setTemplate(savedTemplate.template);
    setInputs(savedTemplate.inputs);
    setDocumentName(savedTemplate.name);
  }, []);

  // Delete template
  const deleteTemplate = useCallback((templateId) => {
    if (confirm('Are you sure you want to delete this template?')) {
      setTemplates(prev => prev.filter(t => t.id !== templateId));
    }
  }, []);

  // Download PDF
  const downloadPDF = useCallback(async () => {
    const pdf = await generatePDF();
    if (pdf) {
      const blob = new Blob([pdf.buffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${documentName}.pdf`;
      a.click();
      URL.revokeObjectURL(url);
    }
  }, [generatePDF, documentName]);

  // Update input values
  const updateInput = useCallback((field, value) => {
    setInputs(prev => {
      const newInputs = [...prev];
      newInputs[0] = { ...newInputs[0], [field]: value };
      return newInputs;
    });
  }, []);

  // Initialize components when mode changes
  useEffect(() => {
    if (!template) return;

    const timer = setTimeout(() => {
      if (currentMode === 'designer') {
        initializeDesigner();
      } else if (currentMode === 'viewer') {
        initializeViewer();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [currentMode, template, initializeDesigner, initializeViewer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (designerInstance) {
        designerInstance.destroy();
      }
      if (viewerInstance) {
        viewerInstance.destroy();
      }
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [designerInstance, viewerInstance, pdfUrl]);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center mb-6">
            <FileText className="w-8 h-8 text-blue-600 mr-3" />
            <h1 className="text-xl font-bold">PDFme Editor</h1>
          </div>

          {/* Document Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Name
            </label>
            <input
              type="text"
              value={documentName}
              onChange={(e) => setDocumentName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Mode Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mode
            </label>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => setCurrentMode('designer')}
                className={`flex flex-col items-center p-3 rounded-md border transition-colors ${
                  currentMode === 'designer'
                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Layout className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">Design</span>
              </button>
              <button
                onClick={() => setCurrentMode('viewer')}
                className={`flex flex-col items-center p-3 rounded-md border transition-colors ${
                  currentMode === 'viewer'
                    ? 'bg-green-50 border-green-300 text-green-700'
                    : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Eye className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">Preview</span>
              </button>
              <button
                onClick={() => setCurrentMode('generator')}
                className={`flex flex-col items-center p-3 rounded-md border transition-colors ${
                  currentMode === 'generator'
                    ? 'bg-purple-50 border-purple-300 text-purple-700'
                    : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                }`}
              >
                <FileText className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">Generate</span>
              </button>
            </div>
          </div>

          {/* Template Actions */}
          <div className="space-y-3 mb-6">
            <button
              onClick={createBlankTemplate}
              disabled={isLoading}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Template
            </button>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <Upload className="w-4 h-4 mr-2" />
              Load PDF
            </button>

            <button
              onClick={saveTemplate}
              disabled={!template || isLoading}
              className="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50 transition-colors"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </button>

            <button
              onClick={downloadPDF}
              disabled={!template || isLoading}
              className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              {isLoading ? 'Generating...' : 'Download PDF'}
            </button>
          </div>

          {/* Input Fields (for all modes) */}
          {template && inputs.length > 0 && inputs[0] && Object.keys(inputs[0]).length > 0 && (
            <div className="border-t pt-6 mb-6">
              <h3 className="font-medium text-gray-900 mb-4">Content Fields</h3>
              <div className="space-y-3">
                {Object.keys(inputs[0]).map((field) => {
                  const fieldSchema = template.schemas[0]?.[field];
                  const isMultiline = fieldSchema?.height > 50;
                  
                  return (
                    <div key={field}>
                      <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                        {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </label>
                      {isMultiline ? (
                        <textarea
                          value={inputs[0][field] || ''}
                          onChange={(e) => updateInput(field, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                          rows={3}
                          placeholder={`Enter ${field}...`}
                        />
                      ) : (
                        <input
                          type="text"
                          value={inputs[0][field] || ''}
                          onChange={(e) => updateInput(field, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder={`Enter ${field}...`}
                        />
                      )}
                    </div>
                  );
                })}
              </div>
              
              {currentMode === 'generator' && (
                <button
                  onClick={generatePDF}
                  disabled={isLoading}
                  className="w-full mt-4 flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 transition-colors"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  {isLoading ? 'Generating...' : 'Generate Preview'}
                </button>
              )}
            </div>
          )}

          {/* Saved Templates */}
          <div className="border-t pt-6">
            <h3 className="font-medium text-gray-900 mb-4">Saved Templates</h3>
            {templates.length === 0 ? (
              <p className="text-sm text-gray-500">No templates saved yet</p>
            ) : (
              <div className="space-y-2">
                {templates.map((tmpl) => (
                  <div
                    key={tmpl.id}
                    className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div
                        onClick={() => loadSavedTemplate(tmpl)}
                        className="flex-1 cursor-pointer"
                      >
                        <div className="font-medium text-sm text-gray-900">
                          {tmpl.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(tmpl.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <button
                        onClick={() => deleteTemplate(tmpl.id)}
                        className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full overflow-hidden">
          {/* Designer Mode */}
          {currentMode === 'designer' && (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-blue-50">
                <h2 className="text-lg font-semibold flex items-center text-blue-800">
                  <Layout className="w-5 h-5 mr-2" />
                  Template Designer
                </h2>
                <p className="text-sm text-blue-600 mt-1">Drag and drop elements to design your PDF template</p>
              </div>
              <div ref={designerRef} className="flex-1 overflow-hidden" />
            </div>
          )}

          {/* Viewer Mode */}
          {currentMode === 'viewer' && (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-green-50">
                <h2 className="text-lg font-semibold flex items-center text-green-800">
                  <Eye className="w-5 h-5 mr-2" />
                  PDF Preview
                </h2>
                <p className="text-sm text-green-600 mt-1">Preview your PDF with current data</p>
              </div>
              <div ref={viewerRef} className="flex-1 overflow-hidden" />
            </div>
          )}

          {/* Generator Mode */}
          {currentMode === 'generator' && (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-purple-50">
                <h2 className="text-lg font-semibold flex items-center text-purple-800">
                  <FileText className="w-5 h-5 mr-2" />
                  PDF Generator
                </h2>
                <p className="text-sm text-purple-600 mt-1">Fill in the fields and generate your PDF</p>
              </div>
              <div className="flex-1 p-6 overflow-auto">
                {pdfUrl ? (
                  <iframe
                    src={pdfUrl}
                    className="w-full h-full border border-gray-300 rounded-md"
                    title="Generated PDF"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full border-2 border-dashed border-gray-300 rounded-md">
                    <div className="text-center">
                      <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600 mb-2">Fill in the fields and generate PDF</p>
                      <p className="text-sm text-gray-500">Use the form in the sidebar to add content</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        onChange={loadPDFTemplate}
        className="hidden"
      />
    </div>
  );
};

export default App;