{"name": "tinymce-react-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "postinstall": "node ./postinstall.js", "preview": "vite preview"}, "dependencies": {"@pdfme/common": "^5.4.2", "@pdfme/generator": "^5.4.2", "@pdfme/schemas": "^5.4.2", "@pdfme/ui": "^5.4.2", "@tailwindcss/vite": "^4.1.12", "lucide-react": "^0.542.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}